{"version": 3, "file": "cdn.js", "names": ["__defProp", "Object", "defineProperty", "__export", "target", "all", "name", "get", "enumerable", "configurable", "set", "newValue", "isPluralType", "val", "one", "undefined", "formatDistanceLocale", "lessThanXSeconds", "default", "in", "ago", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "tense", "addSuffix", "comparison", "tokenValue", "replace", "String", "buildFormatLongFn", "args", "arguments", "length", "width", "defaultWidth", "format", "formats", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "time", "dateTime", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "formatRelative", "_date", "_baseDate", "_options", "buildLocalizeFn", "value", "context", "valuesArray", "formattingValues", "defaultFormattingWidth", "values", "index", "argument<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "localize", "era", "quarter", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "buildMatchFn", "string", "matchPattern", "matchPatterns", "defaultMatchWidth", "matchResult", "match", "matchedString", "parsePatterns", "defaultParseWidth", "key", "Array", "isArray", "findIndex", "pattern", "test", "<PERSON><PERSON><PERSON>", "valueCallback", "rest", "slice", "object", "predicate", "prototype", "hasOwnProperty", "call", "array", "buildMatchPatternFn", "parseResult", "parsePattern", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "parseInt", "ta", "code", "weekStartsOn", "firstWeekContainsDate", "window", "dateFns", "_objectSpread", "locale", "_window$dateFns"], "sources": ["cdn.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, {\n      get: all[name],\n      enumerable: true,\n      configurable: true,\n      set: (newValue) => all[name] = () => newValue\n    });\n};\n\n// lib/locale/ta/_lib/formatDistance.js\nfunction isPluralType(val) {\n  return val.one !== undefined;\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"\\u0B92\\u0BB0\\u0BC1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"\\u0B92\\u0BB0\\u0BC1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"\\u0B92\\u0BB0\\u0BC1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xSeconds: {\n    one: {\n      default: \"1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\",\n      in: \"1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0BAF\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BB5\\u0BBF\\u0BA8\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BB5\\u0BBF\\u0BA8\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  halfAMinute: {\n    default: \"\\u0B85\\u0BB0\\u0BC8 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD\",\n    in: \"\\u0B85\\u0BB0\\u0BC8 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n    ago: \"\\u0B85\\u0BB0\\u0BC8 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n  },\n  lessThanXMinutes: {\n    one: {\n      default: \"\\u0B92\\u0BB0\\u0BC1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"\\u0B92\\u0BB0\\u0BC1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"\\u0B92\\u0BB0\\u0BC1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xMinutes: {\n    one: {\n      default: \"1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\"\n    }\n  },\n  xHours: {\n    one: {\n      default: \"1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"{{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xDays: {\n    one: {\n      default: \"1 \\u0BA8\\u0BBE\\u0BB3\\u0BCD\",\n      in: \"1 \\u0BA8\\u0BBE\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BA8\\u0BBE\\u0BB3\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BA8\\u0BBE\\u0B9F\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BA8\\u0BBE\\u0B9F\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BA8\\u0BBE\\u0B9F\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BBE\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xWeeks: {\n    one: {\n      default: \"1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BB5\\u0BBE\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXMonths: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BBE\\u0BA4\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BBE\\u0BA4\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BBE\\u0BA4\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xMonths: {\n    one: {\n      default: \"1 \\u0BAE\\u0BBE\\u0BA4\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BAE\\u0BBE\\u0BA4\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BAE\\u0BBE\\u0BA4\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xYears: {\n    one: {\n      default: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"1 \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  overXYears: {\n    one: {\n      default: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC7\\u0BB2\\u0BCD\",\n      in: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0BAE\\u0BC7\\u0BB2\\u0BBE\\u0B95\",\n      ago: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0BAE\\u0BC7\\u0BB2\\u0BBE\\u0B95\",\n      in: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  almostXYears: {\n    one: {\n      default: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F 1 \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  }\n};\nvar formatDistance = (token, count, options) => {\n  const tense = options?.addSuffix ? options.comparison && options.comparison > 0 ? \"in\" : \"ago\" : \"default\";\n  const tokenValue = formatDistanceLocale[token];\n  if (!isPluralType(tokenValue))\n    return tokenValue[tense];\n  if (count === 1) {\n    return tokenValue.one[tense];\n  } else {\n    return tokenValue.other[tense].replace(\"{{count}}\", String(count));\n  }\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return (options = {}) => {\n    const width = options.width ? String(options.width) : args.defaultWidth;\n    const format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/ta/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, d MMMM, y\",\n  long: \"d MMMM, y\",\n  medium: \"d MMM, y\",\n  short: \"d/M/yy\"\n};\nvar timeFormats = {\n  full: \"a h:mm:ss zzzz\",\n  long: \"a h:mm:ss z\",\n  medium: \"a h:mm:ss\",\n  short: \"a h:mm\"\n};\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};\n\n// lib/locale/ta/_lib/formatRelative.js\nvar formatRelativeLocale = {\n  lastWeek: \"'\\u0B95\\u0B9F\\u0BA8\\u0BCD\\u0BA4' eeee p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  yesterday: \"'\\u0BA8\\u0BC7\\u0BB1\\u0BCD\\u0BB1\\u0BC1 ' p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  today: \"'\\u0B87\\u0BA9\\u0BCD\\u0BB1\\u0BC1 ' p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  tomorrow: \"'\\u0BA8\\u0BBE\\u0BB3\\u0BC8 ' p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  nextWeek: \"eeee p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  other: \"P\"\n};\nvar formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return (value, options) => {\n    const context = options?.context ? String(options.context) : \"standalone\";\n    let valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      const defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      const width = options?.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      const defaultWidth = args.defaultWidth;\n      const width = options?.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[width] || args.values[defaultWidth];\n    }\n    const index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/ta/_lib/localize.js\nvar eraValues = {\n  narrow: [\"\\u0B95\\u0BBF.\\u0BAE\\u0BC1.\", \"\\u0B95\\u0BBF.\\u0BAA\\u0BBF.\"],\n  abbreviated: [\"\\u0B95\\u0BBF.\\u0BAE\\u0BC1.\", \"\\u0B95\\u0BBF.\\u0BAA\\u0BBF.\"],\n  wide: [\"\\u0B95\\u0BBF\\u0BB1\\u0BBF\\u0BB8\\u0BCD\\u0BA4\\u0BC1\\u0BB5\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\", \"\\u0B85\\u0BA9\\u0BCD\\u0BA9\\u0BCB \\u0B9F\\u0BCB\\u0BAE\\u0BBF\\u0BA9\\u0BBF\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"\\u0B95\\u0BBE\\u0BB2\\u0BBE.1\", \"\\u0B95\\u0BBE\\u0BB2\\u0BBE.2\", \"\\u0B95\\u0BBE\\u0BB2\\u0BBE.3\", \"\\u0B95\\u0BBE\\u0BB2\\u0BBE.4\"],\n  wide: [\n    \"\\u0B92\\u0BA9\\u0BCD\\u0BB1\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\",\n    \"\\u0B87\\u0BB0\\u0BA3\\u0BCD\\u0B9F\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\",\n    \"\\u0BAE\\u0BC2\\u0BA9\\u0BCD\\u0BB1\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\",\n    \"\\u0BA8\\u0BBE\\u0BA9\\u0BCD\\u0B95\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\"\n  ]\n};\nvar monthValues = {\n  narrow: [\"\\u0B9C\", \"\\u0BAA\\u0BBF\", \"\\u0BAE\\u0BBE\", \"\\u0B8F\", \"\\u0BAE\\u0BC7\", \"\\u0B9C\\u0BC2\", \"\\u0B9C\\u0BC2\", \"\\u0B86\", \"\\u0B9A\\u0BC6\", \"\\u0B85\", \"\\u0BA8\", \"\\u0B9F\\u0BBF\"],\n  abbreviated: [\n    \"\\u0B9C\\u0BA9.\",\n    \"\\u0BAA\\u0BBF\\u0BAA\\u0BCD.\",\n    \"\\u0BAE\\u0BBE\\u0BB0\\u0BCD.\",\n    \"\\u0B8F\\u0BAA\\u0BCD.\",\n    \"\\u0BAE\\u0BC7\",\n    \"\\u0B9C\\u0BC2\\u0BA9\\u0BCD\",\n    \"\\u0B9C\\u0BC2\\u0BB2\\u0BC8\",\n    \"\\u0B86\\u0B95.\",\n    \"\\u0B9A\\u0BC6\\u0BAA\\u0BCD.\",\n    \"\\u0B85\\u0B95\\u0BCD.\",\n    \"\\u0BA8\\u0BB5.\",\n    \"\\u0B9F\\u0BBF\\u0B9A.\"\n  ],\n  wide: [\n    \"\\u0B9C\\u0BA9\\u0BB5\\u0BB0\\u0BBF\",\n    \"\\u0BAA\\u0BBF\\u0BAA\\u0BCD\\u0BB0\\u0BB5\\u0BB0\\u0BBF\",\n    \"\\u0BAE\\u0BBE\\u0BB0\\u0BCD\\u0B9A\\u0BCD\",\n    \"\\u0B8F\\u0BAA\\u0BCD\\u0BB0\\u0BB2\\u0BCD\",\n    \"\\u0BAE\\u0BC7\",\n    \"\\u0B9C\\u0BC2\\u0BA9\\u0BCD\",\n    \"\\u0B9C\\u0BC2\\u0BB2\\u0BC8\",\n    \"\\u0B86\\u0B95\\u0BB8\\u0BCD\\u0B9F\\u0BCD\",\n    \"\\u0B9A\\u0BC6\\u0BAA\\u0BCD\\u0B9F\\u0BAE\\u0BCD\\u0BAA\\u0BB0\\u0BCD\",\n    \"\\u0B85\\u0B95\\u0BCD\\u0B9F\\u0BCB\\u0BAA\\u0BB0\\u0BCD\",\n    \"\\u0BA8\\u0BB5\\u0BAE\\u0BCD\\u0BAA\\u0BB0\\u0BCD\",\n    \"\\u0B9F\\u0BBF\\u0B9A\\u0BAE\\u0BCD\\u0BAA\\u0BB0\\u0BCD\"\n  ]\n};\nvar dayValues = {\n  narrow: [\"\\u0B9E\\u0BBE\", \"\\u0BA4\\u0BBF\", \"\\u0B9A\\u0BC6\", \"\\u0BAA\\u0BC1\", \"\\u0BB5\\u0BBF\", \"\\u0BB5\\u0BC6\", \"\\u0B9A\"],\n  short: [\"\\u0B9E\\u0BBE\", \"\\u0BA4\\u0BBF\", \"\\u0B9A\\u0BC6\", \"\\u0BAA\\u0BC1\", \"\\u0BB5\\u0BBF\", \"\\u0BB5\\u0BC6\", \"\\u0B9A\"],\n  abbreviated: [\"\\u0B9E\\u0BBE\\u0BAF\\u0BBF.\", \"\\u0BA4\\u0BBF\\u0B99\\u0BCD.\", \"\\u0B9A\\u0BC6\\u0BB5\\u0BCD.\", \"\\u0BAA\\u0BC1\\u0BA4.\", \"\\u0BB5\\u0BBF\\u0BAF\\u0BBE.\", \"\\u0BB5\\u0BC6\\u0BB3\\u0BCD.\", \"\\u0B9A\\u0BA9\\u0BBF\"],\n  wide: [\n    \"\\u0B9E\\u0BBE\\u0BAF\\u0BBF\\u0BB1\\u0BC1\",\n    \"\\u0BA4\\u0BBF\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n    \"\\u0B9A\\u0BC6\\u0BB5\\u0BCD\\u0BB5\\u0BBE\\u0BAF\\u0BCD\",\n    \"\\u0BAA\\u0BC1\\u0BA4\\u0BA9\\u0BCD\",\n    \"\\u0BB5\\u0BBF\\u0BAF\\u0BBE\\u0BB4\\u0BA9\\u0BCD\",\n    \"\\u0BB5\\u0BC6\\u0BB3\\u0BCD\\u0BB3\\u0BBF\",\n    \"\\u0B9A\\u0BA9\\u0BBF\"\n  ]\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: \"\\u0BAE\\u0BC1.\\u0BAA\",\n    pm: \"\\u0BAA\\u0BBF.\\u0BAA\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD.\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD.\",\n    morning: \"\\u0B95\\u0BBE.\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF.\",\n    evening: \"\\u0BAE\\u0BBE.\",\n    night: \"\\u0B87\\u0BB0.\"\n  },\n  abbreviated: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  },\n  wide: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: \"\\u0BAE\\u0BC1.\\u0BAA\",\n    pm: \"\\u0BAA\\u0BBF.\\u0BAA\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD.\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD.\",\n    morning: \"\\u0B95\\u0BBE.\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF.\",\n    evening: \"\\u0BAE\\u0BBE.\",\n    night: \"\\u0B87\\u0BB0.\"\n  },\n  abbreviated: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  },\n  wide: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  }\n};\nvar ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n    const matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n    const parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    const key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString)) : findKey(parsePatterns, (pattern) => pattern.test(matchedString));\n    let value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return { value, rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (let key = 0;key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n      return null;\n    const matchedString = matchResult[0];\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n      return null;\n    let value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return { value, rest };\n  };\n}\n\n// lib/locale/ta/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)(வது)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(கி.மு.|கி.பி.)/i,\n  abbreviated: /^(கி\\.?\\s?மு\\.?|கி\\.?\\s?பி\\.?)/,\n  wide: /^(கிறிஸ்துவுக்கு\\sமுன்|அன்னோ\\sடோமினி)/i\n};\nvar parseEraPatterns = {\n  any: [/கி\\.?\\s?மு\\.?/, /கி\\.?\\s?பி\\.?/]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^காலா.[1234]/i,\n  wide: /^(ஒன்றாம்|இரண்டாம்|மூன்றாம்|நான்காம்) காலாண்டு/i\n};\nvar parseQuarterPatterns = {\n  narrow: [/1/i, /2/i, /3/i, /4/i],\n  any: [\n    /(1|காலா.1|ஒன்றாம்)/i,\n    /(2|காலா.2|இரண்டாம்)/i,\n    /(3|காலா.3|மூன்றாம்)/i,\n    /(4|காலா.4|நான்காம்)/i\n  ]\n};\nvar matchMonthPatterns = {\n  narrow: /^(ஜ|பி|மா|ஏ|மே|ஜூ|ஆ|செ|அ|ந|டி)$/i,\n  abbreviated: /^(ஜன.|பிப்.|மார்.|ஏப்.|மே|ஜூன்|ஜூலை|ஆக.|செப்.|அக்.|நவ.|டிச.)/i,\n  wide: /^(ஜனவரி|பிப்ரவரி|மார்ச்|ஏப்ரல்|மே|ஜூன்|ஜூலை|ஆகஸ்ட்|செப்டம்பர்|அக்டோபர்|நவம்பர்|டிசம்பர்)/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n    /^ஜ$/i,\n    /^பி/i,\n    /^மா/i,\n    /^ஏ/i,\n    /^மே/i,\n    /^ஜூ/i,\n    /^ஜூ/i,\n    /^ஆ/i,\n    /^செ/i,\n    /^அ/i,\n    /^ந/i,\n    /^டி/i\n  ],\n  any: [\n    /^ஜன/i,\n    /^பி/i,\n    /^மா/i,\n    /^ஏ/i,\n    /^மே/i,\n    /^ஜூன்/i,\n    /^ஜூலை/i,\n    /^ஆ/i,\n    /^செ/i,\n    /^அ/i,\n    /^ந/i,\n    /^டி/i\n  ]\n};\nvar matchDayPatterns = {\n  narrow: /^(ஞா|தி|செ|பு|வி|வெ|ச)/i,\n  short: /^(ஞா|தி|செ|பு|வி|வெ|ச)/i,\n  abbreviated: /^(ஞாயி.|திங்.|செவ்.|புத.|வியா.|வெள்.|சனி)/i,\n  wide: /^(ஞாயிறு|திங்கள்|செவ்வாய்|புதன்|வியாழன்|வெள்ளி|சனி)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^ஞா/i, /^தி/i, /^செ/i, /^பு/i, /^வி/i, /^வெ/i, /^ச/i],\n  any: [/^ஞா/i, /^தி/i, /^செ/i, /^பு/i, /^வி/i, /^வெ/i, /^ச/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(மு.ப|பி.ப|நள்|நண்|காலை|மதியம்|மாலை|இரவு)/i,\n  any: /^(மு.ப|பி.ப|முற்பகல்|பிற்பகல்|நள்ளிரவு|நண்பகல்|காலை|மதியம்|மாலை|இரவு)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^மு/i,\n    pm: /^பி/i,\n    midnight: /^நள்/i,\n    noon: /^நண்/i,\n    morning: /காலை/i,\n    afternoon: /மதியம்/i,\n    evening: /மாலை/i,\n    night: /இரவு/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/ta.js\nvar ta = {\n  code: \"ta\",\n  formatDistance,\n  formatLong,\n  formatRelative,\n  localize,\n  match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 4\n  }\n};\n\n// lib/locale/ta/cdn.js\nwindow.dateFns = {\n  ...window.dateFns,\n  locale: {\n    ...window.dateFns?.locale,\n    ta\n  }\n};\n\n//# debugId=37224F769AD45E0A64756E2164756E21\n"], "mappings": "knDAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAEC,GAAG,EAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG;EAClBL,SAAS,CAACI,MAAM,EAAEE,IAAI,EAAE;IACtBC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IACdE,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,GAAG,EAAE,SAAAA,IAACC,QAAQ,UAAKN,GAAG,CAACC,IAAI,CAAC,GAAG,oBAAMK,QAAQ;EAC/C,CAAC,CAAC;AACN,CAAC;;AAED;AACA,SAASC,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACC,GAAG,KAAKC,SAAS;AAC9B;AACA,IAAIC,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBH,GAAG,EAAE;MACHI,OAAO,EAAE,4HAA4H;MACrIC,EAAE,EAAE,6FAA6F;MACjGC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,qIAAqI;MAC9IC,EAAE,EAAE,sGAAsG;MAC1GC,GAAG,EAAE;IACP;EACF,CAAC;EACDE,QAAQ,EAAE;IACRR,GAAG,EAAE;MACHI,OAAO,EAAE,wCAAwC;MACjDC,EAAE,EAAE,gEAAgE;MACpEC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,kEAAkE;MAC3EC,EAAE,EAAE,8EAA8E;MAClFC,GAAG,EAAE;IACP;EACF,CAAC;EACDG,WAAW,EAAE;IACXL,OAAO,EAAE,+DAA+D;IACxEC,EAAE,EAAE,uFAAuF;IAC3FC,GAAG,EAAE;EACP,CAAC;EACDI,gBAAgB,EAAE;IAChBV,GAAG,EAAE;MACHI,OAAO,EAAE,0JAA0J;MACnKC,EAAE,EAAE,+GAA+G;MACnHC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,uJAAuJ;MAChKC,EAAE,EAAE,4GAA4G;MAChHC,GAAG,EAAE;IACP;EACF,CAAC;EACDK,QAAQ,EAAE;IACRX,GAAG,EAAE;MACHI,OAAO,EAAE,8CAA8C;MACvDC,EAAE,EAAE,sEAAsE;MAC1EC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,wEAAwE;MACjFC,EAAE,EAAE,oFAAoF;MACxFC,GAAG,EAAE;IACP;EACF,CAAC;EACDM,WAAW,EAAE;IACXZ,GAAG,EAAE;MACHI,OAAO,EAAE,0FAA0F;MACnGC,EAAE,EAAE,kHAAkH;MACtHC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,kGAAkG;MAC3GC,EAAE,EAAE,2KAA2K;MAC/KC,GAAG,EAAE;IACP;EACF,CAAC;EACDO,MAAM,EAAE;IACNb,GAAG,EAAE;MACHI,OAAO,EAAE,qDAAqD;MAC9DC,EAAE,EAAE,6EAA6E;MACjFC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,6DAA6D;MACtEC,EAAE,EAAE,qFAAqF;MACzFC,GAAG,EAAE;IACP;EACF,CAAC;EACDQ,KAAK,EAAE;IACLd,GAAG,EAAE;MACHI,OAAO,EAAE,4BAA4B;MACrCC,EAAE,EAAE,wCAAwC;MAC5CC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,sDAAsD;MAC/DC,EAAE,EAAE,kEAAkE;MACtEC,GAAG,EAAE;IACP;EACF,CAAC;EACDS,WAAW,EAAE;IACXf,GAAG,EAAE;MACHI,OAAO,EAAE,uEAAuE;MAChFC,EAAE,EAAE,+FAA+F;MACnGC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,iGAAiG;MAC1GC,EAAE,EAAE,6GAA6G;MACjHC,GAAG,EAAE;IACP;EACF,CAAC;EACDU,MAAM,EAAE;IACNhB,GAAG,EAAE;MACHI,OAAO,EAAE,kCAAkC;MAC3CC,EAAE,EAAE,0DAA0D;MAC9DC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,4DAA4D;MACrEC,EAAE,EAAE,wEAAwE;MAC5EC,GAAG,EAAE;IACP;EACF,CAAC;EACDW,YAAY,EAAE;IACZjB,GAAG,EAAE;MACHI,OAAO,EAAE,uEAAuE;MAChFC,EAAE,EAAE,+FAA+F;MACnGC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,iGAAiG;MAC1GC,EAAE,EAAE,6GAA6G;MACjHC,GAAG,EAAE;IACP;EACF,CAAC;EACDY,OAAO,EAAE;IACPlB,GAAG,EAAE;MACHI,OAAO,EAAE,kCAAkC;MAC3CC,EAAE,EAAE,0DAA0D;MAC9DC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,4DAA4D;MACrEC,EAAE,EAAE,wEAAwE;MAC5EC,GAAG,EAAE;IACP;EACF,CAAC;EACDa,WAAW,EAAE;IACXnB,GAAG,EAAE;MACHI,OAAO,EAAE,6EAA6E;MACtFC,EAAE,EAAE,mFAAmF;MACvFC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,iGAAiG;MAC1GC,EAAE,EAAE,6GAA6G;MACjHC,GAAG,EAAE;IACP;EACF,CAAC;EACDc,MAAM,EAAE;IACNpB,GAAG,EAAE;MACHI,OAAO,EAAE,wCAAwC;MACjDC,EAAE,EAAE,8CAA8C;MAClDC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,4DAA4D;MACrEC,EAAE,EAAE,wEAAwE;MAC5EC,GAAG,EAAE;IACP;EACF,CAAC;EACDe,UAAU,EAAE;IACVrB,GAAG,EAAE;MACHI,OAAO,EAAE,qGAAqG;MAC9GC,EAAE,EAAE,uHAAuH;MAC3HC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,+HAA+H;MACxIC,EAAE,EAAE,wEAAwE;MAC5EC,GAAG,EAAE;IACP;EACF,CAAC;EACDgB,YAAY,EAAE;IACZtB,GAAG,EAAE;MACHI,OAAO,EAAE,2GAA2G;MACpHC,EAAE,EAAE,iHAAiH;MACrHC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,+HAA+H;MACxIC,EAAE,EAAE,2IAA2I;MAC/IC,GAAG,EAAE;IACP;EACF;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAK;EAC9C,IAAMC,KAAK,GAAGD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,SAAS,GAAGF,OAAO,CAACG,UAAU,IAAIH,OAAO,CAACG,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,SAAS;EAC1G,IAAMC,UAAU,GAAG5B,oBAAoB,CAACsB,KAAK,CAAC;EAC9C,IAAI,CAAC1B,YAAY,CAACgC,UAAU,CAAC;EAC3B,OAAOA,UAAU,CAACH,KAAK,CAAC;EAC1B,IAAIF,KAAK,KAAK,CAAC,EAAE;IACf,OAAOK,UAAU,CAAC9B,GAAG,CAAC2B,KAAK,CAAC;EAC9B,CAAC,MAAM;IACL,OAAOG,UAAU,CAACvB,KAAK,CAACoB,KAAK,CAAC,CAACI,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EACpE;AACF,CAAC;;AAED;AACA,SAASQ,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,OAAO,YAAkB,KAAjBR,OAAO,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;IAClB,IAAME,KAAK,GAAGX,OAAO,CAACW,KAAK,GAAGL,MAAM,CAACN,OAAO,CAACW,KAAK,CAAC,GAAGH,IAAI,CAACI,YAAY;IACvE,IAAMC,MAAM,GAAGL,IAAI,CAACM,OAAO,CAACH,KAAK,CAAC,IAAIH,IAAI,CAACM,OAAO,CAACN,IAAI,CAACI,YAAY,CAAC;IACrE,OAAOC,MAAM;EACf,CAAC;AACH;;AAEA;AACA,IAAIE,WAAW,GAAG;EAChBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAEhB,iBAAiB,CAAC;IACtBO,OAAO,EAAEC,WAAW;IACpBH,YAAY,EAAE;EAChB,CAAC,CAAC;EACFY,IAAI,EAAEjB,iBAAiB,CAAC;IACtBO,OAAO,EAAEM,WAAW;IACpBR,YAAY,EAAE;EAChB,CAAC,CAAC;EACFa,QAAQ,EAAElB,iBAAiB,CAAC;IAC1BO,OAAO,EAAEO,eAAe;IACxBT,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;;AAED;AACA,IAAIc,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,sFAAsF;EAChGC,SAAS,EAAE,wFAAwF;EACnGC,KAAK,EAAE,kFAAkF;EACzFC,QAAQ,EAAE,4EAA4E;EACtFC,QAAQ,EAAE,qDAAqD;EAC/DlD,KAAK,EAAE;AACT,CAAC;AACD,IAAImD,cAAc,GAAG,SAAjBA,cAAcA,CAAIlC,KAAK,EAAEmC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,UAAKT,oBAAoB,CAAC5B,KAAK,CAAC;;AAEvF;AACA,SAASsC,eAAeA,CAAC5B,IAAI,EAAE;EAC7B,OAAO,UAAC6B,KAAK,EAAErC,OAAO,EAAK;IACzB,IAAMsC,OAAO,GAAGtC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEsC,OAAO,GAAGhC,MAAM,CAACN,OAAO,CAACsC,OAAO,CAAC,GAAG,YAAY;IACzE,IAAIC,WAAW;IACf,IAAID,OAAO,KAAK,YAAY,IAAI9B,IAAI,CAACgC,gBAAgB,EAAE;MACrD,IAAM5B,YAAY,GAAGJ,IAAI,CAACiC,sBAAsB,IAAIjC,IAAI,CAACI,YAAY;MACrE,IAAMD,KAAK,GAAGX,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEW,KAAK,GAAGL,MAAM,CAACN,OAAO,CAACW,KAAK,CAAC,GAAGC,YAAY;MACnE2B,WAAW,GAAG/B,IAAI,CAACgC,gBAAgB,CAAC7B,KAAK,CAAC,IAAIH,IAAI,CAACgC,gBAAgB,CAAC5B,YAAY,CAAC;IACnF,CAAC,MAAM;MACL,IAAMA,aAAY,GAAGJ,IAAI,CAACI,YAAY;MACtC,IAAMD,MAAK,GAAGX,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEW,KAAK,GAAGL,MAAM,CAACN,OAAO,CAACW,KAAK,CAAC,GAAGH,IAAI,CAACI,YAAY;MACxE2B,WAAW,GAAG/B,IAAI,CAACkC,MAAM,CAAC/B,MAAK,CAAC,IAAIH,IAAI,CAACkC,MAAM,CAAC9B,aAAY,CAAC;IAC/D;IACA,IAAM+B,KAAK,GAAGnC,IAAI,CAACoC,gBAAgB,GAAGpC,IAAI,CAACoC,gBAAgB,CAACP,KAAK,CAAC,GAAGA,KAAK;IAC1E,OAAOE,WAAW,CAACI,KAAK,CAAC;EAC3B,CAAC;AACH;;AAEA;AACA,IAAIE,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,4BAA4B,EAAE,4BAA4B,CAAC;EACpEC,WAAW,EAAE,CAAC,4BAA4B,EAAE,4BAA4B,CAAC;EACzEC,IAAI,EAAE,CAAC,+GAA+G,EAAE,qEAAqE;AAC/L,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,4BAA4B,EAAE,4BAA4B,EAAE,4BAA4B,EAAE,4BAA4B,CAAC;EACrIC,IAAI,EAAE;EACJ,6FAA6F;EAC7F,mGAAmG;EACnG,mGAAmG;EACnG,mGAAmG;;AAEvG,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC;EAC1KC,WAAW,EAAE;EACX,eAAe;EACf,2BAA2B;EAC3B,2BAA2B;EAC3B,qBAAqB;EACrB,cAAc;EACd,0BAA0B;EAC1B,0BAA0B;EAC1B,eAAe;EACf,2BAA2B;EAC3B,qBAAqB;EACrB,eAAe;EACf,qBAAqB,CACtB;;EACDC,IAAI,EAAE;EACJ,gCAAgC;EAChC,kDAAkD;EAClD,sCAAsC;EACtC,sCAAsC;EACtC,cAAc;EACd,0BAA0B;EAC1B,0BAA0B;EAC1B,sCAAsC;EACtC,8DAA8D;EAC9D,kDAAkD;EAClD,4CAA4C;EAC5C,kDAAkD;;AAEtD,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,CAAC;EAClH3B,KAAK,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,CAAC;EACjH4B,WAAW,EAAE,CAAC,2BAA2B,EAAE,2BAA2B,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,2BAA2B,EAAE,2BAA2B,EAAE,oBAAoB,CAAC;EAC3MC,IAAI,EAAE;EACJ,sCAAsC;EACtC,4CAA4C;EAC5C,kDAAkD;EAClD,gCAAgC;EAChC,4CAA4C;EAC5C,sCAAsC;EACtC,oBAAoB;;AAExB,CAAC;AACD,IAAII,eAAe,GAAG;EACpBN,MAAM,EAAE;IACNO,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE,qBAAqB;IACzBC,QAAQ,EAAE,qBAAqB;IAC/BC,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE;EACT,CAAC;EACDb,WAAW,EAAE;IACXM,EAAE,EAAE,kDAAkD;IACtDC,EAAE,EAAE,kDAAkD;IACtDC,QAAQ,EAAE,kDAAkD;IAC5DC,IAAI,EAAE,4CAA4C;IAClDC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,sCAAsC;IACjDC,OAAO,EAAE,0BAA0B;IACnCC,KAAK,EAAE;EACT,CAAC;EACDZ,IAAI,EAAE;IACJK,EAAE,EAAE,kDAAkD;IACtDC,EAAE,EAAE,kDAAkD;IACtDC,QAAQ,EAAE,kDAAkD;IAC5DC,IAAI,EAAE,4CAA4C;IAClDC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,sCAAsC;IACjDC,OAAO,EAAE,0BAA0B;IACnCC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9Bf,MAAM,EAAE;IACNO,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE,qBAAqB;IACzBC,QAAQ,EAAE,qBAAqB;IAC/BC,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE;EACT,CAAC;EACDb,WAAW,EAAE;IACXM,EAAE,EAAE,kDAAkD;IACtDC,EAAE,EAAE,kDAAkD;IACtDC,QAAQ,EAAE,kDAAkD;IAC5DC,IAAI,EAAE,4CAA4C;IAClDC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,sCAAsC;IACjDC,OAAO,EAAE,0BAA0B;IACnCC,KAAK,EAAE;EACT,CAAC;EACDZ,IAAI,EAAE;IACJK,EAAE,EAAE,kDAAkD;IACtDC,EAAE,EAAE,kDAAkD;IACtDC,QAAQ,EAAE,kDAAkD;IAC5DC,IAAI,EAAE,4CAA4C;IAClDC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,sCAAsC;IACjDC,OAAO,EAAE,0BAA0B;IACnCC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,WAAW,EAAE5B,QAAQ,EAAK;EAC7C,OAAO7B,MAAM,CAACyD,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbF,aAAa,EAAbA,aAAa;EACbG,GAAG,EAAE7B,eAAe,CAAC;IACnBM,MAAM,EAAEG,SAAS;IACjBjC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFsD,OAAO,EAAE9B,eAAe,CAAC;IACvBM,MAAM,EAAEO,aAAa;IACrBrC,YAAY,EAAE,MAAM;IACpBgC,gBAAgB,EAAE,SAAAA,iBAACsB,OAAO,UAAKA,OAAO,GAAG,CAAC;EAC5C,CAAC,CAAC;EACFC,KAAK,EAAE/B,eAAe,CAAC;IACrBM,MAAM,EAAEQ,WAAW;IACnBtC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFwD,GAAG,EAAEhC,eAAe,CAAC;IACnBM,MAAM,EAAES,SAAS;IACjBvC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFyD,SAAS,EAAEjC,eAAe,CAAC;IACzBM,MAAM,EAAEU,eAAe;IACvBxC,YAAY,EAAE,MAAM;IACpB4B,gBAAgB,EAAEqB,yBAAyB;IAC3CpB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;;AAED;AACA,SAAS6B,YAAYA,CAAC9D,IAAI,EAAE;EAC1B,OAAO,UAAC+D,MAAM,EAAmB,KAAjBvE,OAAO,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;IAC1B,IAAME,KAAK,GAAGX,OAAO,CAACW,KAAK;IAC3B,IAAM6D,YAAY,GAAG7D,KAAK,IAAIH,IAAI,CAACiE,aAAa,CAAC9D,KAAK,CAAC,IAAIH,IAAI,CAACiE,aAAa,CAACjE,IAAI,CAACkE,iBAAiB,CAAC;IACrG,IAAMC,WAAW,GAAGJ,MAAM,CAACK,KAAK,CAACJ,YAAY,CAAC;IAC9C,IAAI,CAACG,WAAW,EAAE;MAChB,OAAO,IAAI;IACb;IACA,IAAME,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;IACpC,IAAMG,aAAa,GAAGnE,KAAK,IAAIH,IAAI,CAACsE,aAAa,CAACnE,KAAK,CAAC,IAAIH,IAAI,CAACsE,aAAa,CAACtE,IAAI,CAACuE,iBAAiB,CAAC;IACtG,IAAMC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACJ,aAAa,CAAC,GAAGK,SAAS,CAACL,aAAa,EAAE,UAACM,OAAO,UAAKA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC,GAAC,GAAGS,OAAO,CAACR,aAAa,EAAE,UAACM,OAAO,UAAKA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC,GAAC;IAChL,IAAIxC,KAAK;IACTA,KAAK,GAAG7B,IAAI,CAAC+E,aAAa,GAAG/E,IAAI,CAAC+E,aAAa,CAACP,GAAG,CAAC,GAAGA,GAAG;IAC1D3C,KAAK,GAAGrC,OAAO,CAACuF,aAAa,GAAGvF,OAAO,CAACuF,aAAa,CAAClD,KAAK,CAAC,GAAGA,KAAK;IACpE,IAAMmD,IAAI,GAAGjB,MAAM,CAACkB,KAAK,CAACZ,aAAa,CAACnE,MAAM,CAAC;IAC/C,OAAO,EAAE2B,KAAK,EAALA,KAAK,EAAEmD,IAAI,EAAJA,IAAI,CAAC,CAAC;EACxB,CAAC;AACH;AACA,SAASF,OAAOA,CAACI,MAAM,EAAEC,SAAS,EAAE;EAClC,KAAK,IAAMX,GAAG,IAAIU,MAAM,EAAE;IACxB,IAAIjI,MAAM,CAACmI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEV,GAAG,CAAC,IAAIW,SAAS,CAACD,MAAM,CAACV,GAAG,CAAC,CAAC,EAAE;MAC/E,OAAOA,GAAG;IACZ;EACF;EACA;AACF;AACA,SAASG,SAASA,CAACY,KAAK,EAAEJ,SAAS,EAAE;EACnC,KAAK,IAAIX,GAAG,GAAG,CAAC,EAACA,GAAG,GAAGe,KAAK,CAACrF,MAAM,EAAEsE,GAAG,EAAE,EAAE;IAC1C,IAAIW,SAAS,CAACI,KAAK,CAACf,GAAG,CAAC,CAAC,EAAE;MACzB,OAAOA,GAAG;IACZ;EACF;EACA;AACF;;AAEA;AACA,SAASgB,mBAAmBA,CAACxF,IAAI,EAAE;EACjC,OAAO,UAAC+D,MAAM,EAAmB,KAAjBvE,OAAO,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;IAC1B,IAAMkE,WAAW,GAAGJ,MAAM,CAACK,KAAK,CAACpE,IAAI,CAACgE,YAAY,CAAC;IACnD,IAAI,CAACG,WAAW;IACd,OAAO,IAAI;IACb,IAAME,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;IACpC,IAAMsB,WAAW,GAAG1B,MAAM,CAACK,KAAK,CAACpE,IAAI,CAAC0F,YAAY,CAAC;IACnD,IAAI,CAACD,WAAW;IACd,OAAO,IAAI;IACb,IAAI5D,KAAK,GAAG7B,IAAI,CAAC+E,aAAa,GAAG/E,IAAI,CAAC+E,aAAa,CAACU,WAAW,CAAC,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;IACpF5D,KAAK,GAAGrC,OAAO,CAACuF,aAAa,GAAGvF,OAAO,CAACuF,aAAa,CAAClD,KAAK,CAAC,GAAGA,KAAK;IACpE,IAAMmD,IAAI,GAAGjB,MAAM,CAACkB,KAAK,CAACZ,aAAa,CAACnE,MAAM,CAAC;IAC/C,OAAO,EAAE2B,KAAK,EAALA,KAAK,EAAEmD,IAAI,EAAJA,IAAI,CAAC,CAAC;EACxB,CAAC;AACH;;AAEA;AACA,IAAIW,yBAAyB,GAAG,eAAe;AAC/C,IAAIC,yBAAyB,GAAG,MAAM;AACtC,IAAIC,gBAAgB,GAAG;EACrBvD,MAAM,EAAE,mBAAmB;EAC3BC,WAAW,EAAE,gCAAgC;EAC7CC,IAAI,EAAE;AACR,CAAC;AACD,IAAIsD,gBAAgB,GAAG;EACrBC,GAAG,EAAE,CAAC,eAAe,EAAE,eAAe;AACxC,CAAC;AACD,IAAIC,oBAAoB,GAAG;EACzB1D,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,eAAe;EAC5BC,IAAI,EAAE;AACR,CAAC;AACD,IAAIyD,oBAAoB,GAAG;EACzB3D,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCyD,GAAG,EAAE;EACH,qBAAqB;EACrB,sBAAsB;EACtB,sBAAsB;EACtB,sBAAsB;;AAE1B,CAAC;AACD,IAAIG,kBAAkB,GAAG;EACvB5D,MAAM,EAAE,kCAAkC;EAC1CC,WAAW,EAAE,+DAA+D;EAC5EC,IAAI,EAAE;AACR,CAAC;AACD,IAAI2D,kBAAkB,GAAG;EACvB7D,MAAM,EAAE;EACN,MAAM;EACN,MAAM;EACN,MAAM;EACN,KAAK;EACL,MAAM;EACN,MAAM;EACN,MAAM;EACN,KAAK;EACL,MAAM;EACN,KAAK;EACL,KAAK;EACL,MAAM,CACP;;EACDyD,GAAG,EAAE;EACH,MAAM;EACN,MAAM;EACN,MAAM;EACN,KAAK;EACL,MAAM;EACN,QAAQ;EACR,QAAQ;EACR,KAAK;EACL,MAAM;EACN,KAAK;EACL,KAAK;EACL,MAAM;;AAEV,CAAC;AACD,IAAIK,gBAAgB,GAAG;EACrB9D,MAAM,EAAE,yBAAyB;EACjC3B,KAAK,EAAE,yBAAyB;EAChC4B,WAAW,EAAE,4CAA4C;EACzDC,IAAI,EAAE;AACR,CAAC;AACD,IAAI6D,gBAAgB,GAAG;EACrB/D,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAC/DyD,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC7D,CAAC;AACD,IAAIO,sBAAsB,GAAG;EAC3BhE,MAAM,EAAE,6CAA6C;EACrDyD,GAAG,EAAE;AACP,CAAC;AACD,IAAIQ,sBAAsB,GAAG;EAC3BR,GAAG,EAAE;IACHlD,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,KAAK,GAAG;EACVd,aAAa,EAAEkC,mBAAmB,CAAC;IACjCxB,YAAY,EAAE2B,yBAAyB;IACvCD,YAAY,EAAEE,yBAAyB;IACvCb,aAAa,EAAE,SAAAA,cAAClD,KAAK,UAAK2E,QAAQ,CAAC3E,KAAK,EAAE,EAAE,CAAC;EAC/C,CAAC,CAAC;EACF4B,GAAG,EAAEK,YAAY,CAAC;IAChBG,aAAa,EAAE4B,gBAAgB;IAC/B3B,iBAAiB,EAAE,MAAM;IACzBI,aAAa,EAAEwB,gBAAgB;IAC/BvB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFb,OAAO,EAAEI,YAAY,CAAC;IACpBG,aAAa,EAAE+B,oBAAoB;IACnC9B,iBAAiB,EAAE,MAAM;IACzBI,aAAa,EAAE2B,oBAAoB;IACnC1B,iBAAiB,EAAE,KAAK;IACxBQ,aAAa,EAAE,SAAAA,cAAC5C,KAAK,UAAKA,KAAK,GAAG,CAAC;EACrC,CAAC,CAAC;EACFwB,KAAK,EAAEG,YAAY,CAAC;IAClBG,aAAa,EAAEiC,kBAAkB;IACjChC,iBAAiB,EAAE,MAAM;IACzBI,aAAa,EAAE6B,kBAAkB;IACjC5B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFX,GAAG,EAAEE,YAAY,CAAC;IAChBG,aAAa,EAAEmC,gBAAgB;IAC/BlC,iBAAiB,EAAE,MAAM;IACzBI,aAAa,EAAE+B,gBAAgB;IAC/B9B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFV,SAAS,EAAEC,YAAY,CAAC;IACtBG,aAAa,EAAEqC,sBAAsB;IACrCpC,iBAAiB,EAAE,KAAK;IACxBI,aAAa,EAAEiC,sBAAsB;IACrChC,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC;;AAED;AACA,IAAIkC,EAAE,GAAG;EACPC,IAAI,EAAE,IAAI;EACVrH,cAAc,EAAdA,cAAc;EACdyB,UAAU,EAAVA,UAAU;EACVU,cAAc,EAAdA,cAAc;EACdgC,QAAQ,EAARA,QAAQ;EACRY,KAAK,EAALA,KAAK;EACL5E,OAAO,EAAE;IACPmH,YAAY,EAAE,CAAC;IACfC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACAC,MAAM,CAACC,OAAO,GAAAC,aAAA,CAAAA,aAAA;AACTF,MAAM,CAACC,OAAO;EACjBE,MAAM,EAAAD,aAAA,CAAAA,aAAA,MAAAE,eAAA;EACDJ,MAAM,CAACC,OAAO,cAAAG,eAAA,uBAAdA,eAAA,CAAgBD,MAAM;IACzBP,EAAE,EAAFA,EAAE,GACH,GACF;;;;AAED", "ignoreList": []}