import { formatDistance } from "./sv/_lib/formatDistance.js";
import { formatLong } from "./sv/_lib/formatLong.js";
import { formatRelative } from "./sv/_lib/formatRelative.js";
import { localize } from "./sv/_lib/localize.js";
import { match } from "./sv/_lib/match.js";

/**
 * @category Locales
 * @summary Swedish locale.
 * @language Swedish
 * @iso-639-2 swe
 * <AUTHOR> [@ejulen](https://github.com/ejulen)
 * <AUTHOR> [@alexandernanberg](https://github.com/alexandernanberg)
 * <AUTHOR> [@limelights](https://github.com/limelights)
 */
export const sv = {
  code: "sv",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default sv;
